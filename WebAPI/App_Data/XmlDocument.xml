<?xml version="1.0"?>
<doc>
    <assembly>
        <name>WebApi</name>
    </assembly>
    <members>
        <member name="T:Pari.Audit.ErrorLog">
            <summary>
            This class is used to log and view the application errors occurring from code behind.
            </summary>
        </member>
        <member name="M:Pari.Audit.ErrorLog.LogError(System.Exception)">
            <summary>
            Used to log the error in database table. Generally this method should be called from catch block of try-catch block code.
            </summary> 
            <param name="ex">Exception type to be passed that is handled in catch block</param>
        </member>
        <member name="M:Pari.Audit.ErrorLog.GetErrorLog(System.String,System.String,System.Nullable{System.Boolean})">
            <summary>
            Used to get the error log saved in the database table.
            </summary> 
            <param name="fromDate">(optional) Date to be supplied in "dd MMM yyyy" format. This parameter helps to get log starting from this date.</param>
            <param name="toDate">(optional) Date to be supplied in "dd MMM yyyy" format. This parameter helps to get log uptill this date.</param>
            <param name="isAcknowledged">(optional) Boolean value to get log that is acknowledged or not.</param>
            <returns></returns>
        </member>
        <member name="M:Pari.Audit.ErrorLog.UpdateAcknowledgement(System.Int32,System.Boolean,System.String)">
            <summary>
            Used to update the IS_ACKNOWLEDGED flag in database for supplied unique Id
            </summary> 
            <param name="id">Unique Id to update.</param>
            <param name="isAcknowledged">Whether acknowledged or not.</param>
            <param name="remark"></param>
        </member>
        <member name="T:Pari.Newtonsoft.Conversions.Convertor">
            <summary>
            This class includes methods and properties that are used for object conversion created with the help of Newtonsoft.dll.
            </summary>
        </member>
        <member name="M:Pari.Newtonsoft.Conversions.Convertor.SerializeJsonObject(System.Object)">
            <summary>
            Serializes Object into Json String
            </summary>
            <param name="obj">Object that needs to be serialized into Json string</param>
            <returns>String in Json format</returns>
            <remarks></remarks>
        </member>
        <member name="M:Pari.Newtonsoft.Conversions.Convertor.DeserializeJsonObject``1(System.String)">
            <summary>
            Deserializes Json String into Object of Type T. Here T is Generic Type
            </summary>
            <typeparam name="T">Any Object type into which the Json string needs to be converted</typeparam>
            <param name="jsonString">String in Json format</param>
            <returns>Object of Type T. Here T is Generic Type</returns>
            <remarks></remarks>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.ApiDescriptionExtensions.GetFriendlyId(System.Web.Http.Description.ApiDescription)">
            <summary>
            Generates an URI-friendly ID for the <see cref="T:System.Web.Http.Description.ApiDescription"/>. E.g. "Get-Values-id_name" instead of "GetValues/{id}?name={name}"
            </summary>
            <param name="description">The <see cref="T:System.Web.Http.Description.ApiDescription"/>.</param>
            <returns>The ID as a string.</returns>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.HelpPageConfig">
            <summary>
            Use this class to customize the Help Page.
            For example you can set a custom <see cref="T:System.Web.Http.Description.IDocumentationProvider"/> to supply the documentation
            or you can provide the samples for the requests/responses.
            </summary>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.Controllers.HelpController">
            <summary>
            The controller that will handle requests for the help page.
            </summary>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetDocumentationProvider(System.Web.Http.HttpConfiguration,System.Web.Http.Description.IDocumentationProvider)">
            <summary>
            Sets the documentation provider for help page.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="documentationProvider">The documentation provider.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetSampleObjects(System.Web.Http.HttpConfiguration,System.Collections.Generic.IDictionary{System.Type,System.Object})">
            <summary>
            Sets the objects that will be used by the formatters to produce sample requests/responses.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="sampleObjects">The sample objects.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetSampleRequest(System.Web.Http.HttpConfiguration,System.Object,System.Net.Http.Headers.MediaTypeHeaderValue,System.String,System.String)">
            <summary>
            Sets the sample request directly for the specified media type and action.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="sample">The sample request.</param>
            <param name="mediaType">The media type.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetSampleRequest(System.Web.Http.HttpConfiguration,System.Object,System.Net.Http.Headers.MediaTypeHeaderValue,System.String,System.String,System.String[])">
            <summary>
            Sets the sample request directly for the specified media type and action with parameters.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="sample">The sample request.</param>
            <param name="mediaType">The media type.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
            <param name="parameterNames">The parameter names.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetSampleResponse(System.Web.Http.HttpConfiguration,System.Object,System.Net.Http.Headers.MediaTypeHeaderValue,System.String,System.String)">
            <summary>
            Sets the sample request directly for the specified media type of the action.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="sample">The sample response.</param>
            <param name="mediaType">The media type.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetSampleResponse(System.Web.Http.HttpConfiguration,System.Object,System.Net.Http.Headers.MediaTypeHeaderValue,System.String,System.String,System.String[])">
            <summary>
            Sets the sample response directly for the specified media type of the action with specific parameters.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="sample">The sample response.</param>
            <param name="mediaType">The media type.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
            <param name="parameterNames">The parameter names.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetSampleForMediaType(System.Web.Http.HttpConfiguration,System.Object,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Sets the sample directly for all actions with the specified media type.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="sample">The sample.</param>
            <param name="mediaType">The media type.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetSampleForType(System.Web.Http.HttpConfiguration,System.Object,System.Net.Http.Headers.MediaTypeHeaderValue,System.Type)">
            <summary>
            Sets the sample directly for all actions with the specified type and media type.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="sample">The sample.</param>
            <param name="mediaType">The media type.</param>
            <param name="type">The parameter type or return type of an action.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetActualRequestType(System.Web.Http.HttpConfiguration,System.Type,System.String,System.String)">
            <summary>
            Specifies the actual type of <see cref="T:System.Net.Http.ObjectContent`1"/> passed to the <see cref="T:System.Net.Http.HttpRequestMessage"/> in an action.
            The help page will use this information to produce more accurate request samples.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="type">The type.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetActualRequestType(System.Web.Http.HttpConfiguration,System.Type,System.String,System.String,System.String[])">
            <summary>
            Specifies the actual type of <see cref="T:System.Net.Http.ObjectContent`1"/> passed to the <see cref="T:System.Net.Http.HttpRequestMessage"/> in an action.
            The help page will use this information to produce more accurate request samples.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="type">The type.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
            <param name="parameterNames">The parameter names.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetActualResponseType(System.Web.Http.HttpConfiguration,System.Type,System.String,System.String)">
            <summary>
            Specifies the actual type of <see cref="T:System.Net.Http.ObjectContent`1"/> returned as part of the <see cref="T:System.Net.Http.HttpRequestMessage"/> in an action.
            The help page will use this information to produce more accurate response samples.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="type">The type.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetActualResponseType(System.Web.Http.HttpConfiguration,System.Type,System.String,System.String,System.String[])">
            <summary>
            Specifies the actual type of <see cref="T:System.Net.Http.ObjectContent`1"/> returned as part of the <see cref="T:System.Net.Http.HttpRequestMessage"/> in an action.
            The help page will use this information to produce more accurate response samples.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="type">The type.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
            <param name="parameterNames">The parameter names.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.GetHelpPageSampleGenerator(System.Web.Http.HttpConfiguration)">
            <summary>
            Gets the help page sample generator.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <returns>The help page sample generator.</returns>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.SetHelpPageSampleGenerator(System.Web.Http.HttpConfiguration,Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator)">
            <summary>
            Sets the help page sample generator.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="sampleGenerator">The help page sample generator.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.GetModelDescriptionGenerator(System.Web.Http.HttpConfiguration)">
            <summary>
            Gets the model description generator.
            </summary>
            <param name="config">The configuration.</param>
            <returns>The <see cref="T:Pari.WebApi.Areas.HelpPage.ModelDescriptions.ModelDescriptionGenerator"/></returns>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageConfigurationExtensions.GetHelpPageApiModel(System.Web.Http.HttpConfiguration,System.String)">
            <summary>
            Gets the model that represents an API displayed on the help page. The model is initialized on the first call and cached for subsequent calls.
            </summary>
            <param name="config">The <see cref="T:System.Web.Http.HttpConfiguration"/>.</param>
            <param name="apiDescriptionId">The <see cref="T:System.Web.Http.Description.ApiDescription"/> ID.</param>
            <returns>
            An <see cref="T:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel"/>
            </returns>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.ModelDescriptions.ModelDescription">
            <summary>
            Describes a type model.
            </summary>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.ModelDescriptions.ModelDescriptionGenerator">
            <summary>
            Generates model descriptions for given types.
            </summary>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.ModelDescriptions.ModelNameAttribute">
            <summary>
            Use this attribute to change the name of the <see cref="T:Pari.WebApi.Areas.HelpPage.ModelDescriptions.ModelDescription"/> generated for a type.
            </summary>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel">
            <summary>
            The model that represents an API displayed on the help page.
            </summary>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel"/> class.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.ApiDescription">
            <summary>
            Gets or sets the <see cref="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.ApiDescription"/> that describes the API.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.UriParameters">
            <summary>
            Gets or sets the <see cref="T:Pari.WebApi.Areas.HelpPage.ModelDescriptions.ParameterDescription"/> collection that describes the URI parameters for the API.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.RequestDocumentation">
            <summary>
            Gets or sets the documentation for the request.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.RequestModelDescription">
            <summary>
            Gets or sets the <see cref="T:Pari.WebApi.Areas.HelpPage.ModelDescriptions.ModelDescription"/> that describes the request body.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.RequestBodyParameters">
            <summary>
            Gets the request body parameter descriptions.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.ResourceDescription">
            <summary>
            Gets or sets the <see cref="T:Pari.WebApi.Areas.HelpPage.ModelDescriptions.ModelDescription"/> that describes the resource.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.ResourceProperties">
            <summary>
            Gets the resource property descriptions.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.SampleRequests">
            <summary>
            Gets the sample requests associated with the API.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.SampleResponses">
            <summary>
            Gets the sample responses associated with the API.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.Models.HelpPageApiModel.ErrorMessages">
            <summary>
            Gets the error messages associated with this model.
            </summary>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator">
            <summary>
            This class will generate the samples for the help page.
            </summary>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator"/> class.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.ActualHttpMessageTypes">
            <summary>
            Gets CLR types that are used as the content of <see cref="T:System.Net.Http.HttpRequestMessage"/> or <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.ActionSamples">
            <summary>
            Gets the objects that are used directly as samples for certain actions.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.SampleObjects">
            <summary>
            Gets the objects that are serialized as samples by the supported formatters.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.SampleObjectFactories">
            <summary>
            Gets factories for the objects that the supported formatters will serialize as samples. Processed in order,
            stopping when the factory successfully returns a non-<see langref="null"/> object.
            </summary>
            <remarks>
            Collection includes just <see cref="M:Pari.WebApi.Areas.HelpPage.ObjectGenerator.GenerateObject(System.Type)"/> initially. Use
            <code>SampleObjectFactories.Insert(0, func)</code> to provide an override and
            <code>SampleObjectFactories.Add(func)</code> to provide a fallback.</remarks>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.GetSampleRequests(System.Web.Http.Description.ApiDescription)">
            <summary>
            Gets the request body samples for a given <see cref="T:System.Web.Http.Description.ApiDescription"/>.
            </summary>
            <param name="api">The <see cref="T:System.Web.Http.Description.ApiDescription"/>.</param>
            <returns>The samples keyed by media type.</returns>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.GetSampleResponses(System.Web.Http.Description.ApiDescription)">
            <summary>
            Gets the response body samples for a given <see cref="T:System.Web.Http.Description.ApiDescription"/>.
            </summary>
            <param name="api">The <see cref="T:System.Web.Http.Description.ApiDescription"/>.</param>
            <returns>The samples keyed by media type.</returns>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.GetSample(System.Web.Http.Description.ApiDescription,Pari.WebApi.Areas.HelpPage.SampleDirection)">
            <summary>
            Gets the request or response body samples.
            </summary>
            <param name="api">The <see cref="T:System.Web.Http.Description.ApiDescription"/>.</param>
            <param name="sampleDirection">The value indicating whether the sample is for a request or for a response.</param>
            <returns>The samples keyed by media type.</returns>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.GetActionSample(System.String,System.String,System.Collections.Generic.IEnumerable{System.String},System.Type,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue,Pari.WebApi.Areas.HelpPage.SampleDirection)">
            <summary>
            Search for samples that are provided directly through <see cref="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.ActionSamples"/>.
            </summary>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
            <param name="parameterNames">The parameter names.</param>
            <param name="type">The CLR type.</param>
            <param name="formatter">The formatter.</param>
            <param name="mediaType">The media type.</param>
            <param name="sampleDirection">The value indicating whether the sample is for a request or for a response.</param>
            <returns>The sample that matches the parameters.</returns>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.GetSampleObject(System.Type)">
            <summary>
            Gets the sample object that will be serialized by the formatters. 
            First, it will look at the <see cref="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.SampleObjects"/>. If no sample object is found, it will try to create
            one using <see cref="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.DefaultSampleObjectFactory(Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator,System.Type)"/> (which wraps an <see cref="T:Pari.WebApi.Areas.HelpPage.ObjectGenerator"/>) and other
            factories in <see cref="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.SampleObjectFactories"/>.
            </summary>
            <param name="type">The type.</param>
            <returns>The sample object.</returns>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.ResolveHttpRequestMessageType(System.Web.Http.Description.ApiDescription)">
            <summary>
            Resolves the actual type of <see cref="T:System.Net.Http.ObjectContent`1"/> passed to the <see cref="T:System.Net.Http.HttpRequestMessage"/> in an action.
            </summary>
            <param name="api">The <see cref="T:System.Web.Http.Description.ApiDescription"/>.</param>
            <returns>The type.</returns>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.ResolveType(System.Web.Http.Description.ApiDescription,System.String,System.String,System.Collections.Generic.IEnumerable{System.String},Pari.WebApi.Areas.HelpPage.SampleDirection,System.Collections.ObjectModel.Collection{System.Net.Http.Formatting.MediaTypeFormatter}@)">
            <summary>
            Resolves the type of the action parameter or return value when <see cref="T:System.Net.Http.HttpRequestMessage"/> or <see cref="T:System.Net.Http.HttpResponseMessage"/> is used.
            </summary>
            <param name="api">The <see cref="T:System.Web.Http.Description.ApiDescription"/>.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
            <param name="parameterNames">The parameter names.</param>
            <param name="sampleDirection">The value indicating whether the sample is for a request or a response.</param>
            <param name="formatters">The formatters.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleGenerator.WriteSampleObjectUsingFormatter(System.Net.Http.Formatting.MediaTypeFormatter,System.Object,System.Type,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Writes the sample object using formatter.
            </summary>
            <param name="formatter">The formatter.</param>
            <param name="value">The value.</param>
            <param name="type">The type.</param>
            <param name="mediaType">Type of the media.</param>
            <returns></returns>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey">
            <summary>
            This is used to identify the place where the sample should be applied.
            </summary>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Creates a new <see cref="T:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey"/> based on media type.
            </summary>
            <param name="mediaType">The media type.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue,System.Type)">
            <summary>
            Creates a new <see cref="T:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey"/> based on media type and CLR type.
            </summary>
            <param name="mediaType">The media type.</param>
            <param name="type">The CLR type.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.#ctor(Pari.WebApi.Areas.HelpPage.SampleDirection,System.String,System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new <see cref="T:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey"/> based on <see cref="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.SampleDirection"/>, controller name, action name and parameter names.
            </summary>
            <param name="sampleDirection">The <see cref="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.SampleDirection"/>.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
            <param name="parameterNames">The parameter names.</param>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue,Pari.WebApi.Areas.HelpPage.SampleDirection,System.String,System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new <see cref="T:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey"/> based on media type, <see cref="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.SampleDirection"/>, controller name, action name and parameter names.
            </summary>
            <param name="mediaType">The media type.</param>
            <param name="sampleDirection">The <see cref="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.SampleDirection"/>.</param>
            <param name="controllerName">Name of the controller.</param>
            <param name="actionName">Name of the action.</param>
            <param name="parameterNames">The parameter names.</param>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.ControllerName">
            <summary>
            Gets the name of the controller.
            </summary>
            <value>
            The name of the controller.
            </value>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.ActionName">
            <summary>
            Gets the name of the action.
            </summary>
            <value>
            The name of the action.
            </value>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.MediaType">
            <summary>
            Gets the media type.
            </summary>
            <value>
            The media type.
            </value>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.ParameterNames">
            <summary>
            Gets the parameter names.
            </summary>
        </member>
        <member name="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.SampleDirection">
            <summary>
            Gets the <see cref="P:Pari.WebApi.Areas.HelpPage.HelpPageSampleKey.SampleDirection"/>.
            </summary>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.ImageSample">
            <summary>
            This represents an image sample on the help page. There's a display template named ImageSample associated with this class.
            </summary>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.ImageSample.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Pari.WebApi.Areas.HelpPage.ImageSample"/> class.
            </summary>
            <param name="src">The URL of an image.</param>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.InvalidSample">
            <summary>
            This represents an invalid sample on the help page. There's a display template named InvalidSample associated with this class.
            </summary>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.ObjectGenerator">
            <summary>
            This class will create an object of a given type and populate it with sample data.
            </summary>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.ObjectGenerator.GenerateObject(System.Type)">
            <summary>
            Generates an object for a given type. The type needs to be public, have a public default constructor and settable public properties/fields. Currently it supports the following types:
            Simple types: <see cref="T:System.Int32"/>, <see cref="T:System.String"/>, <see cref="T:System.Enum"/>, <see cref="T:System.DateTime"/>, <see cref="T:System.Uri"/>, etc.
            Complex types: POCO types.
            Nullables: <see cref="T:System.Nullable`1"/>.
            Arrays: arrays of simple types or complex types.
            Key value pairs: <see cref="T:System.Collections.Generic.KeyValuePair`2"/>
            Tuples: <see cref="T:System.Tuple`1"/>, <see cref="T:System.Tuple`2"/>, etc
            Dictionaries: <see cref="T:System.Collections.Generic.IDictionary`2"/> or anything deriving from <see cref="T:System.Collections.Generic.IDictionary`2"/>.
            Collections: <see cref="T:System.Collections.Generic.IList`1"/>, <see cref="T:System.Collections.Generic.IEnumerable`1"/>, <see cref="T:System.Collections.Generic.ICollection`1"/>, <see cref="T:System.Collections.IList"/>, <see cref="T:System.Collections.IEnumerable"/>, <see cref="T:System.Collections.ICollection"/> or anything deriving from <see cref="T:System.Collections.Generic.ICollection`1"/> or <see cref="T:System.Collections.IList"/>.
            Queryables: <see cref="T:System.Linq.IQueryable"/>, <see cref="T:System.Linq.IQueryable`1"/>.
            </summary>
            <param name="type">The type.</param>
            <returns>An object of the given type.</returns>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.SampleDirection">
            <summary>
            Indicates whether the sample is used for request or response
            </summary>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.TextSample">
            <summary>
            This represents a preformatted text sample on the help page. There's a display template named TextSample associated with this class.
            </summary>
        </member>
        <member name="T:Pari.WebApi.Areas.HelpPage.XmlDocumentationProvider">
            <summary>
            A custom <see cref="T:System.Web.Http.Description.IDocumentationProvider"/> that reads the API documentation from an XML documentation file.
            </summary>
        </member>
        <member name="M:Pari.WebApi.Areas.HelpPage.XmlDocumentationProvider.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Pari.WebApi.Areas.HelpPage.XmlDocumentationProvider"/> class.
            </summary>
            <param name="documentPath">The physical path to XML document.</param>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.GetAllPagesDetails">
            <summary>
            Gets all Page details.
            </summary> 
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.GetAllPagesFilterDetails(System.Int32,System.String,System.Boolean)">
            <summary>
            Get all page filter details with roleId, userId and userAllowed provided as input.
            </summary> 
            <param name="roleId"></param>
            <param name="userId"></param>
            <param name="userAllowed"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.InsertPagePath(System.Net.Http.HttpRequestMessage)">
            <summary>
            Inserts the page path.
            </summary> 
            <param name="httpRequestMessage"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.GetPageMenuDetails">
            <summary>
            Gets the page menu details.
            </summary> 
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.GetPageFeaturesDetails">
            <summary>
            Gets the page feature details.
            </summary> 
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.GetPageFeatureAccessExportDetails">
            <summary>
            Gets page feature access master export details.
            </summary> 
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.GetRoleFilterDetails">
            <summary>
            Get role filter details.
            </summary> 
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.GetUserFilterDetails(System.Int32)">
            <summary>
            Get users details in filter.
            </summary> 
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.GetFilesFilterbyRole(System.Int32)">
            <summary>
            Gets files details filtered by role.
            </summary> 
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.GetFilesFilterbyUser(System.String)">
            <summary>
            Gets files details filtered by user.
            </summary> 
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.SetPageAsApplicableOrNot(System.Net.Http.HttpRequestMessage)">
            <summary>
            Set page as applicable or not.
            </summary> 
            <param name="httpRequestMessage"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageAccessMasterController.DeletePageDetails(System.Int32)">
            <summary>
            Delete page details with pageId provided as input.
            </summary> 
            <param name="pageId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.GetRoleWithFeature(System.Int32)">
            <summary>
            Gets the Roles details with pageId provided as input.
            </summary> 
            <param name="pageId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.GetFeatureWithRole(System.Int32)">
            <summary>
            Gets the Feature details with pageId provided as input.
            </summary> 
            <param name="pageId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.GetAccessDetails(System.Int32)">
            <summary>
            Gets the Access grid details with pageId provided as input.
            </summary> 
            <param name="pageId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.GetUsersByRole(System.Nullable{System.Int16},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Gets the User details with roleId, featureId and pageId provided as input.
            </summary> 
            <param name="roleId"></param>
            <param name="featureId"></param>
            <param name="pageId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.InsertPageFeatureAccessControl(System.Net.Http.HttpRequestMessage)">
            <summary>
            Inserts the feature assigned to role and visa versa.
            </summary> 
            <param name="httpRequestMessage"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.GetFeaturesByRole(System.Int32,System.Nullable{System.Int16})">
            <summary>
            Gets the Feature details which is assigned to role.
            </summary> 
            <param name="pageId"></param>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.GetRolesByFeature(System.Int32,System.Int32)">
            <summary>
            Gets the Role details which is assigned to feature.
            </summary> 
            <param name="pageId"></param>
            <param name="featureId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.DeletePageFeatureAccesss(System.Int32,System.Nullable{System.Int16},System.Nullable{System.Int32})">
            <summary>
            Delete the row in Access datagrid with roleId, featureId and pageId provided as input.
            </summary> 
            <param name="pageId"></param>
            <param name="roleId"></param>
            <param name="featureId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.GetPageFeatureAccessExportDetailsByPage(System.Nullable{System.Decimal})">
            <summary>
            Gets the Page feature export details with pageId provided as input.
            </summary> 
            <param name="pageId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureAccessController.GetUserDenyList(System.Int32,System.Nullable{System.Int16},System.Nullable{System.Int32})">
            <summary>
            Gets the list of user Denied with roleId, featureId and pageId provided as input.
            </summary> 
            <param name="pageId"></param>
            <param name="roleId"></param>
            <param name="featureId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureController.GetPageFeatureDetails(System.Int32)">
            <summary>
            Gets the page feature details
            </summary> 
            <param name="pageId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureController.InsertPageFeatureDetails(System.Net.Http.HttpRequestMessage)">
            <summary>
            Insert the page feature
            </summary> 
            <param name="httpRequestMessage"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureController.UpdatePageFeatureDetails(System.Net.Http.HttpRequestMessage)">
            <summary>
            Update the page feature
            </summary> 
            <param name="httpRequestMessage"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureController.DeletePageFeatures(System.Int32)">
            <summary>
            Delete the page feature
            </summary> 
            <param name="featureId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Controllers.PageFeaturesAccessControl.PageFeatureController.IsValidPageFeature(System.String,System.Int32,System.Int32)">
            <summary>
            Checks whether inserted/updated page feature is valid or not.
            </summary> 
            <param name="feature"></param>
            <param name="featureId"></param>
            <param name="pageId"></param>
            <returns></returns>
        </member>
        <member name="M:Pari.MPT.WebApi.Models.Global.AddDataWithParameter(System.Collections.Generic.Dictionary{System.String,System.Data.SqlClient.SqlParameter}@,System.String,System.Data.SqlDbType,System.Data.ParameterDirection,System.Object)">
            <summary>
            This method create sql parameter with their value details
            </summary>
            <param name="sqlParameters"></param>
            <param name="parameterName"></param>
            <param name="sqlDbType"></param>
            <param name="parameterDirection"></param>
            <param name="parameterValue"></param>
        </member>
        <member name="M:Pari.MPT.WebApi.Models.Global.IsReferredInReferenceTables(System.String,System.String,System.String,System.String)">
            <summary>
            This method used for check id is referred in another table or not
            </summary> 
            <param name="schemaName"></param>
            <param name="tableName"></param>
            <param name="id"></param>
            <param name="unwantedTable"></param>
            <returns></returns>
        </member>
        <member name="T:Pari.MPT.WebApi.Models.DBParameters.DBParameter">
            <summary>
            Contains the Parameter properties which are saved in Database.
            </summary>
        </member>
    </members>
</doc>
