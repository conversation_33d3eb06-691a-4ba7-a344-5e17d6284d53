<%@ page title="" language="C#" masterpagefile="~/Site.Master" autoeventwireup="true" codebehind="ParameterMaster.aspx.cs" inherits="WebApplication.System_Admin.ParameterMaster" %>

<%@ register src="~/UserControls/ActivityLogPopup.ascx" tagprefix="uc" tagname="AuditLogPopup" %>
<asp:Content ID="Content1" ContentPlaceHolderID="HeadContent" runat="Server">
    <style type="text/css">
        .dx-placeholder {
            color: gray !important;
            line-height: 0.5;
        }

        /*.HeaderTextNote {
            color: black;
            Font-Size: X-Small;
            font: Verdana;
        }*/
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="Server">
    <asp:ScriptManager ID="ScriptManager" runat="server" />
    <asp:PlaceHolder ID="PlaceHolder1" runat="server">
        <%: <PERSON>.Render(BundleConfig.DevxtremeStyleBundlePath) %>
        <%: Scripts.Render("~/Scripts/DevExtreme/ParameterMasterScripts") %>
        <script type="text/javascript">
            var viewport = getViewPortHeightWidth();
            var gridParameterMasterDataSource;
            var apiRootUri = GetRootURI() + "/";
            var gridParameterMaster;
            var gridParameterMasterViewOptions = $.extend(true, {}, gridOptions);
            var gridExportViewOptions = $.extend(true, {}, gridOptions);
            var isIdColumnVisible = false;
            var parameterPopup;
            var transactionExportDataSource;
            var transactionExportGrid;
            var _parameterList;
            var newRecordRowNo = null;
            var ThemeDetailsDataSource;
            var ChangedBy = '<%= Session["Username"] %>';
            $(function () {
                Bindthems();
                function Bindthems() {
                    ThemeDetailsDataSource = CallAPIInSync("GET", apiRootUri + "ParameterMaster/GetThemeDetails").Data;

                }
                //#region Transaction Export Data Source
                transactionExportDataSource = new DevExpress.data.DataSource({
                    load: function (e) {
                        return CallAPIInSync("Get", apiRootUri + "ParameterMaster/GetExportParameterMasterAndTransaction").Data;
                    },
                });

                transactionExportGrid = $("#transactionExport").dxDataGrid($.extend(true, gridExportViewOptions, {
                    dataSource: transactionExportDataSource,
                    visible: false,
                    headerFilter: {
                        visible: true
                    },
                    columns: [
                        {
                            dataField: "PARAMETER",
                            caption: "Parameter",
                            alignment: "left",
                            dataType: "string"
                        },
                        {
                            dataField: "DESCRIPTION",
                            caption: "Description",
                            alignment: "left",
                            dataType: "string"
                        },
                        {
                            dataField: "PARAMETER_VALUE",
                            caption: "Parameter Value",
                            alignment: "left",
                            dataType: "string"
                        },
                        {
                            dataField: "WEF",
                            caption: "With Effect From",
                            format: 'dd MMM yyyy',
                            alignment: "center",
                            dataType: "date",
                        },
                        {
                            dataField: "TRANSACTION_VALUE",
                            caption: "Transaction Value",
                            alignment: "letf",
                            dataType: "string"
                        },
                        {
                            dataField: "USERNAMEFULLNAME",
                            caption: "Logged By",
                            alignment: "left",
                            dataType: "string",
                        },
                        {
                            dataField: "LOGGEDDATE",
                            caption: "Logged Date",
                            format: 'dd MMM yyyy',
                            alignment: "center",
                            dataType: "date"
                        },
                    ]
                })).dxDataGrid('instance');
                //#endregion

                //Data Source
                gridParameterMasterDataSource = new DevExpress.data.DataSource({
                    key: "ID",
                    load: function (e) {
                        _parameterList = CallAPIInSync("GET", apiRootUri + "ParameterMaster/GetParameterMasterDetails").Data;
                        return _parameterList;
                    },
                    insert: function (values) {
                        openCustomDialog("Confirmation", "<img src='../../../Images/Confirmation.png' style='margin-bottom: 2px;height:16px;width:16px;'></img> Are you sure you want to add ?", ["Yes", "No"]).show().done(function (dialogResult) {
                            if (dialogResult.buttonText == "Yes") {
                                values.ChangedBy = ChangedBy;
                                var uri = apiRootUri + "ParameterMaster/InsertParameterMasterDetails";
                                result = CallAPIInSync("POST", uri, JSON.stringify(values));
                                if (result.Status == "OK") {
                                    ToastMessage("Added successfully.", "Success");

                                    var gridData = $("#gridParameterMaster").dxDataGrid('instance');

                                    gridData.refresh();
                                    gridParameterMasterDataSource.reload();
                                    return result.Data;
                                } else if (result.Status == "Bad Request") {
                                    if (result.Data.Message != "") {
                                        ToastMessage(result.Data.Message, "Warning", "1500", 300, "center");
                                    }
                                    else {
                                        ToastMessage("Something went wrong.Contact System admin and try again.", "Error");
                                    }
                                    return false;
                                }
                            }
                        });
                    },
                    update: function (key, values) {
                        var grid = $("#gridParameterMaster").dxDataGrid('instance');
                        var uri = apiRootUri + "ParameterMaster/UpdateParameterMasterDetails";

                        values.ID = JSON.stringify(key);
                        values.PARAMETER_VALUE = grid.cellValue(grid.getRowIndexByKey(key), "PARAMETER_VALUE");
                        values.DESCRIPTION = grid.cellValue(grid.getRowIndexByKey(key), "DESCRIPTION");
                        values.ChangedBy = ChangedBy;

                        openCustomDialog("Confirmation", "<img src='../../../Images/Confirmation.png' style='margin-bottom: 2px;height:16px;width:16px;'></img> Are you sure you want to update ?", ["Yes", "No"]).show().done(function (dialogResult) {
                            if (dialogResult.buttonText == "Yes") {

                                result = CallAPIInSync("POST", uri, JSON.stringify(values));

                                if (result.Status == "OK") {
                                    ToastMessage("Updated successfully.", "Success");
                                    grid.refresh();
                                    gridParameterMasterDataSource.reload();
                                    return result.Data;
                                } else if (result.Status == "Bad Request") {
                                    if (result.Data.Message != "") {
                                        ToastMessage(result.Data.Message, "Warning", "1500", 300, "center");
                                    }
                                    else {
                                        ToastMessage("Something went wrong. Contact System admin and try again.", "Error");
                                    }
                                    return false;
                                }
                            }
                        });
                    }
                });

                //Instance
                gridParameterMaster = $("#gridParameterMaster").dxDataGrid($.extend(true, gridParameterMasterViewOptions, {
                    dataSource: gridParameterMasterDataSource,
                    height: (viewport.height - 20),
                    width: "100%",
                    allowColumnReordering: true,
                    headerFilter: {
                        visible: true
                    },
                    searchPanel: {
                        visible: true
                    },
                    filterPanel: {
                        visible: true
                    },
                    filterRow: {
                        visible: true,
                        applyFilter: "auto"
                    },
                    columnChooser: {
                        enabled: true
                    },
                    editing: {
                        mode: "row",
                        allowAdding: true,
                        editEnabled: true,
                        allowUpdating: true,
                        useIcons: true,
                        selectTextOnEditStart: true,
                        texts: {
                            addRow: "Add a row",
                        }
                    },
                    onContentReady: function (e) {
                        e.component.columnOption("command:edit", "visibleIndex", -1);
                        e.component.columnOption("command:edit", "width", 50);
                    },
                    export: {
                        enabled: true,
                        proxyUrl: 'ExportHandler.axd',
                        fileName: "Parameter Master"
                    },
                    onExporting: function (e) {
                        if ($("#gridParameterMaster").dxDataGrid('instance')._controllers.data._dataSource.totalCount() == 0) {
                            ToastMessage("No record found !", "info", "3000", 300, { my: 'center', at: 'center', of: '#gridParameterMaster' });
                            e.cancel = true;
                        }
                        else {
                            //transaction Export
                            var transactionGridExport = $("#transactionExport").dxDataGrid("instance");
                            transactionGridExport.beginUpdate();
                            transactionGridExport.option("visible", true);

                            isIdColumnVisible = e.component.columnOption("ID", "visible");
                            e.component.beginUpdate();
                            //   e.component.columnOption("ID", "visible", true);
                            var workbook = new ExcelJS.Workbook();
                            var worksheet = workbook.addWorksheet('Parameter Master');
                            if (gridParameterMaster.getVisibleColumns().length == 6) {
                                worksheet.columns = [
                                    { width: 50 }, { width: 60 }, { width: 75 }, { width: 17 }, { width: 60 }
                                ];
                            }
                            else {
                                worksheet.columns = [
                                    { width: 35 }, { width: 35 }, { width: 30 }
                                ];
                            }
                            //Transaction Work Sheet
                            var parameterTransaction = workbook.addWorksheet('Parameter Transactions');
                            parameterTransaction.columns = [
                                { width: 60 }, { width: 60 }, { width: 60 }, { width: 18 }, { width: 60 }, { width: 30 }, { width: 15 }
                            ];
                            DevExpress.excelExporter.exportDataGrid({
                                component: e.component,
                                worksheet: worksheet,
                                keepColumnWidths: false,
                                topLeftCell: { row: 1, column: 1 }
                            }).then(function () {
                                return DevExpress.excelExporter.exportDataGrid({
                                    worksheet: parameterTransaction,
                                    component: transactionExportGrid,
                                    keepColumnWidths: false
                                });
                            }).then(function () {
                                workbook.xlsx.writeBuffer().then(function (buffer) {
                                    saveAs(new Blob([buffer], { type: "application/octet-stream" }), ("Parameter Master.xlsx"));
                                }).then(function () {
                                    //if (isIdColumnVisible == false) {
                                    //  e.component.columnOption("ID", "visible", false);
                                    //}
                                    e.component.endUpdate();
                                });
                            });
                            e.cancel = true;
                        }
                        transactionExportGrid.option("visible", false);
                        transactionExportGrid.endUpdate();
                    },
                    columns: [
                        {
                            dataField: "ID",
                            caption: "Id",
                            alignment: "center",
                            width: "5%",
                            dataType: "number",
                            allowEditing: false,
                            allowFiltering: true,
                            allowHeaderFiltering: false,
                            headerCellTemplate: $('<Span title="Id">Id</span>'),
                            showInColumnChooser: true,
                            visible: false
                        },
                        {
                            dataField: "PARAMETER",
                            caption: "Parameter",
                            alignment: "left",
                            dataType: "string",
                            allowEditing: true,
                            allowHeaderFiltering: true,
                            sortOrder: 'asc',
                            headerFilter: {
                                dataSource: function (options) {
                                    options.dataSource.postProcess = function (results) {
                                        return results.filter((item) => item.value !== null);
                                    }
                                }
                            },
                            width: "18%",
                            editorOptions: {
                                showClearButton: true,
                            },
                            headerCellTemplate: $('<Span title="Parameter"><i style="color:red">*</i> Parameter</span>'),
                        },
                        {
                            dataField: "PARAMETER_VALUE",
                            caption: "Parameter Value",
                            alignment: "left",
                            dataType: "string",
                            allowEditing: true,
                            allowHeaderFiltering: true,
                            width: "25%",
                            headerCellTemplate: $('<Span title="Parameter Value"><i style="color:red">*</i> Parameter value</span>'),
                            editCellTemplate: function (cellElement, cellInfo) {
                                if (cellInfo.data.PARAMETER === "Theme") {
                                    $("<div>")
                                        .appendTo(cellElement)
                                        .dxLookup({
                                            dataSource: ThemeDetailsDataSource,
                                            displayExpr: "PARAMETER_VALUE",
                                            valueExpr: "PARAMETER_VALUE",
                                            value: cellInfo.value,
                                            onValueChanged: function (e) {
                                                cellInfo.setValue(e.value);
                                            }
                                        });
                                }
                                else {
                                    $("<div>")
                                        .appendTo(cellElement)
                                        .dxTextBox({
                                            value: cellInfo.value, // Bind the current cell value
                                            onValueChanged: function (e) {
                                                cellInfo.setValue(e.value); // Update the cell value
                                            },
                                        });
                                }
                            },
                        },
                        {
                            dataField: "DESCRIPTION",
                            caption: "Description",
                            alignment: "left",
                            dataType: "string",
                            columnAutoWidth: true,
                            allowEditing: true,
                            allowResizing: true,
                            allowHeaderFiltering: true,
                            headerCellTemplate: $('<Span title="Description">Description</span>'),
                            headerFilter: {
                                dataSource: function (options) {
                                    options.dataSource.postProcess = function (results) {
                                        const hasBlank = results.some((item) => item.value === null);
                                        const filteredResults = results.filter((item) => item.value !== null);
                                        return hasBlank ? [{ text: "Blank", value: null }, ...filteredResults] : filteredResults;
                                    }
                                }
                            },
                        },
                        {
                            dataField: "WEF",
                            caption: "With Effect From",
                            format: 'dd MMM yyyy',
                            hint: " Allowed Date Format: MM/dd/yyyy(01/31/2022), dd MMM YYYY(01 Jan 2022)",
                            alignment: "center",
                            dataType: "date",
                            width: "8%",
                            allowHeaderFiltering: false,
                            allowFiltering: true,
                            allowEditing: false,
                            showInColumnChooser: false,
                            visible: false,
                            headerCellTemplate: $('<Span title="With Effect From">WEF</span>'),
                            editorOptions: {
                                hint: " Allowed Date Format: MM/dd/yyyy(01/31/2022), dd MMM YYYY(01 Jan 2022)",
                                showClearButton: true,
                                applyValueMode: 'useButtons'
                            },
                        },
                        {
                            dataField: "EDIT",
                            caption: "Edit",
                            allowHeaderFiltering: false,
                            width: "4%",
                            alignment: "center",
                            allowEditing: false,
                            allowFiltering: false,
                            allowExporting: false,
                            visible: false,
                            showInColumnChooser: false,
                            headerCellTemplate: function (container) {
                                container.append($("<a>", { style: "color:black;text-decoration:none", title: "Parameter Transactions", "href": "#", addClass: "dx-link dx-link-edit dx-icon-edit dx-link-icon " }))
                            },
                            cellTemplate: function (container, options) {
                                if (options.data.ID != null) {
                                    container.append($("<a>", { onclick: "OpenParameterEditPopup('" + options.data.ID + "')", style: "height:16px;width:16px;cursor:pointer;text-decoration:none", title: "Parameter Transactions", "href": "#", addClass: "dx-link dx-link-edit dx-icon-edit dx-link-icon " }))
                                }
                            }
                        },
                    ],
                    onContextMenuPreparing: function (e) {
                        if (e.row != undefined && e.row.rowType === "data") {
                            e.items = [
                                //{
                                //    text: "Edit Parameter Value",
                                //    beginGroup: true,
                                //    icon: "edit",
                                //    onItemClick: function () {
                                //        OpenParameterEditPopup(e.row.data.ID);
                                //        return false;
                                //    }
                                //},
                                {
                                    text: "Refresh",
                                    beginGroup: true,
                                    icon: "refresh",
                                    onItemClick: function () {
                                        gridParameterMasterDataSource.reload();
                                        return false;
                                    }
                                }
                            ]
                        }
                    },
                    onRowValidating: function (e) {

                    },
                    onEditorPreparing: function (e) {
                        if (e.parentType == "dataRow" && e.dataField == "PARAMETER") {
                            var editor = e.component;
                            e.editorOptions.placeholder = "Parameter max limit is 300 characters.";
                        }
                        //if (e.parentType == "dataRow" && e.dataField == "PARAMETER") {
                        //    e.editorName = "dxTextArea";
                        //}
                        if (e.parentType == "dataRow" && e.dataField == "PARAMETER_VALUE") {
                            var editor = e.component;
                            e.editorOptions.placeholder = "Parameter value max limit is 1000 characters.";
                        }
                        //if (e.parentType == "dataRow" && e.dataField == "PARAMETER_VALUE") {
                        //    e.editorName = "dxTextArea";
                        //}
                        if (e.parentType == "dataRow" && e.dataField == "DESCRIPTION") {
                            var editor = e.component;
                            e.editorOptions.placeholder = "Description max limit is 1000 characters.";
                        }
                        //if (e.parentType == "dataRow" && e.dataField == "DESCRIPTION") {
                        //    e.editorName = "dxTextArea";
                        //}
                        if ((e.parentType === "dataRow" && e.dataField == "PARAMETER") && e.row.isNewRow == undefined) {
                            e.editorOptions.readOnly = true;
                        }
                        if (e.dataField === "PARAMETER_VALUE" && e.parentType === "dataRow" && newRecordRowNo != -1) {
                            e.editorOptions.disabled = true;
                        }
                        if (e.dataField === "PARAMETER_VALUE" && e.parentType === "dataRow" && e.row.data.WEF == null && newRecordRowNo != -1) {
                            e.editorOptions.disabled = false;
                        }
                    },
                    onInitNewRow: function (e) {
                        newRecordRowNo = -1;
                    },
                    onSaved: function (e) {
                        newRecordRowNo = null;
                    },
                    onEditCanceled: function (e) {
                        newRecordRowNo = null;
                    },
                    onSaving: function (e) {
                        if (e.changes != undefined && e.changes.length != 0) {
                            if (e.changes[0].type == 'insert') {
                                id = 0;
                                if (e.changes[0].data.PARAMETER === undefined || e.changes[0].data.PARAMETER === null || e.changes[0].data.PARAMETER === '') {
                                    ToastMessage("Parameter cannot be blank.", "Warning");
                                    e.cancel = true;
                                    return;
                                } else if (e.changes[0].data.PARAMETER.length > 300) {
                                    ToastMessage("Parameter max limit exceeded, it should be less than or equal to 300 characters.", "Warning");
                                    e.cancel = true;
                                    return;
                                }

                                if (e.changes[0].data.PARAMETER_VALUE === undefined || e.changes[0].data.PARAMETER_VALUE === null || e.changes[0].data.PARAMETER_VALUE === '') {
                                    ToastMessage("Parameter value cannot be blank.", "Warning");
                                    e.cancel = true;
                                    return;
                                } else if (e.changes[0].data.PARAMETER_VALUE.length > 1000) {
                                    ToastMessage("Parameter value max limit exceeded, it should be less than or equal to 1000 characters.", "Warning");
                                    e.cancel = true;
                                    return;
                                }

                                if (e.changes[0].data.DESCRIPTION != undefined && e.changes[0].data.DESCRIPTION != null && e.changes[0].data.DESCRIPTION != '') {
                                    if (e.changes[0].data.DESCRIPTION.length > 1000) {
                                        ToastMessage("Description max limit exceeded, it should be less than or equal to 1000 characters.", "Warning");
                                        e.cancel = true;
                                        return;
                                    }
                                }

                                if (IsRecordAlreadyExists(e.changes[0].data.PARAMETER, _parameterList)) {
                                    ToastMessage("The given parameter already exists.", "Warning");
                                    e.cancel = true;
                                    return;
                                }
                            }

                            if (e.changes[0].type == 'update') {
                                var grid = $("#gridParameterMaster").dxDataGrid('instance');
                                var _uParameter = grid.cellValue(grid.getRowIndexByKey(e.changes[0].key), "PARAMETER");
                                var _uParameterValue = grid.cellValue(grid.getRowIndexByKey(e.changes[0].key), "PARAMETER_VALUE");
                                var _uDescription = grid.cellValue(grid.getRowIndexByKey(e.changes[0].key), "DESCRIPTION");

                                if (_uParameter === undefined || _uParameter === null || _uParameter === '') {
                                    ToastMessage("Parameter cannot be blank.", "Warning");
                                    e.cancel = true;
                                    return;
                                } else if (_uParameter.length > 300) {
                                    ToastMessage("Parameter max limit exceeded, it should be less than or equal to 300 characters.", "Warning");
                                    e.cancel = true;
                                    return;
                                }

                                if (_uParameterValue === undefined || _uParameterValue === null || _uParameterValue === '') {
                                    ToastMessage("Parameter value cannot be blank.", "Warning");
                                    e.cancel = true;
                                    return;
                                } else if (_uParameterValue.length > 1000) {
                                    ToastMessage("Parameter value max limit exceeded, it should be less than or equal to 1000 characters.", "Warning");
                                    e.cancel = true;
                                    return;
                                }

                                if (_uDescription != undefined && _uDescription != null && _uDescription != '') {
                                    if (_uDescription.length > 1000) {
                                        ToastMessage("Description max limit exceeded, it should be less than or equal to 1000 characters.", "Warning");
                                        e.cancel = true;
                                        return;
                                    }
                                }
                            }
                        }
                    },
                    onToolbarPreparing: function onToolbarPreparing(e) {
                        e.toolbarOptions.items.unshift(
                            {
                                visible: true,
                                location: "after",
                                widget: "dxButton",
                                options: {
                                    icon: "../../../images/activitylog.png",
                                    type: "normal",
                                    hint: "Activity Log",
                                    onClick: function (e) {
                                        ShowAuditLogPopup("ParameterMaster.aspx");
                                    }
                                }
                            }
                        )
                    }
                })).dxDataGrid('instance');

            });

            //show Popup
            function OpenParameterEditPopup(id) {
                parameterPopup = $("#parameterPopup").dxPopup({
                    width: 650,
                    height: 540,
                    title: "Parameter Transactions",
                    contentTemplate: function (content) {
                        $("<iframe id='parameterTransaction' style='width: 100%; height: 100%;border:none;'></iframe>").appendTo(content);
                        return content;
                    },
                    onShown: function (e) {
                        var frame = $("#parameterTransaction");
                        if (frame)
                            $(frame).attr('src', "ParameterTransactions.aspx?parameterId=" + id + "");
                    },
                    onHiding: function (e) {
                        $("#parameterTransaction").removeAttr('src');
                    }
                }).dxPopup("instance");
                parameterPopup.show();
            }

            //hide Popup
            function closeParameterTransactionPopup() {
                gridParameterMasterDataSource.reload();
                transactionExportDataSource.reload();
                parameterPopup.hide();
            };

            function IsRecordAlreadyExists(newRecord, oldParameters) {
                const trimmedRecord = (newRecord || '').trim();
                const isPresent = oldParameters.some(item => (item.PARAMETER || '').trim() === trimmedRecord);
                return isPresent;
            }

        </script>
    </asp:PlaceHolder>
    <asp:UpdatePanel ID="UpdatePanel" runat="server">
        <contenttemplate>
            <uc:auditlogpopup runat="server" id="ucAuditLogPopup" />
            <table width="100%" align="center">
                <tr>
                    <td>
                        <div id="gridParameterMaster">
                        </div>
                    </td>
                </tr>
            </table>
            <div id="parameterPopup"></div>
            <div id="transactionExport"></div>
        </contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

