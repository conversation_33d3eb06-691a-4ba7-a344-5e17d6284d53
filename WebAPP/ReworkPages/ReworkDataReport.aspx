<%@ Page Title="" Language="C#" MasterPageFile="~/NewSite.Master" AutoEventWireup="true" CodeBehind="ReworkDataReport.aspx.cs" Inherits="WebApplication.ReworkPages.ReworkDataReport" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

  <style type="text/css">
  </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
  <asp:ScriptManager ID="ScriptManager" runat="server" />
  <asp:PlaceHolder ID="PlaceHolder1" runat="server">
    <%-- <%: Styles.Render(BundleConfig.DevxtremeStyleBundlePath) %>
    <%: Scripts.Render("~/Scripts/DevExtreme/ReworkDataReportScripts") %>--%>
    <script src="../Scripts/StandardLineAndStationDropdownlist.js"></script>
    <script src="../Scripts/StandardDataGrid.js"></script>
    <script src="../Scripts/Export/exceljs.min.js"></script>
    <script src="../Scripts/bootstrap-3.3.7/docs/assets/js/vendor/FileSaver.js"></script>
    <script src="../Scripts/Export/FileSaver.min.js"></script>
    <%-- <script src="../Scripts/WebApi.js"></script>--%>
    <script type="text/javascript">
      var getdata;
      var srnodata;
      var datagrid;
      var gridContainer;
      var res;
      var gridReworkDataSource;
      var gridDelayMasterExport;
      var Username ='<%=ConfigurationManager.AppSettings["UN"]%>';
      var Password ='<%=ConfigurationManager.AppSettings["Pass"]%>';
      var customers;
      var customers1;
      var viewport;
      var apiRootUri = "";
      var datas;
      var pagename = '<%= Session["pagename"] %>';
      var timerInterval;
      var timerInterval1;
      var data1;
      var globalstn;
      var authToken = "";
      var stationval;
      var txtcompsrno;
      var Serialnumarr;
      var modes;

      $(function () {

        apiRootUri = GetRootURI() + "/";

        stationval = '';

        function GetRootURI() {
          return callWebMethod(root + "Web Services/StandardFunctions.asmx/GetAPIRootURI", "");
        }

        viewport = getViewPortHeightWidth();

        var tooken = callWebmethod("Login.aspx/GetTookenDetails", data);
        tooken = JSON.parse(tooken)
        authToken = tooken.access_token;

        var data = {
          userName: Username.toLowerCase(),
          password: Password, grantType: "password",
          domainId: ""
        };

        BindComponentSrNo();

        const priorities = ['Auto', 'Manual'];
        const priorityEntities = [
          { id: 1, text: 'Auto' },
          { id: 2, text: 'Manual' },

        ];

        $('#radio-group-change-layout').dxRadioGroup({
          items: priorityEntities,
          valueExpr: 'id',
          displayExpr: 'text',
          layout: 'horizontal',
          value: priorityEntities[0].id,
          onInitialized: function (e) {
            var defaultValue = e.component.option("value");
            if (defaultValue == 1) {

              modes = defaultValue;

              bindStation();

              startTimer();
            }
            else {
              modes = defaultValue;
              $("#txtcompsrno").dxselectbox("option", "disabled", false);

              clearInterval(timerInterval);

              bindStation();

            }
          },
          onValueChanged(e) {
            if (e.value == 1) {
              modes = e.value;
              $("#txtcompsrno").dxSelectBox("option", "disabled", true);
              $("#txtcompsrno").dxSelectBox("instance").option("value", null);
              bindStation();
              startTimer();
            }
            else {
              modes = e.value;
              $("#txtcompsrno").dxSelectBox("option", "disabled", false);
              clearInterval(timerInterval);
              bindStation();
            }
          }
        });

        function bindStation() {
          $.ajax({
            url: apiRootUri + "StationMaster/GetStationAndLine",
            method: "GET",
            headers: {
              "Authorization": "Bearer " + authToken
            },
            success: function (response, status, xhr) {

              if (response && xhr.status === 200) {

                var stndata = response;
                stndata1 = stndata;
                data1 = stndata;

                var stnvalue = stndata == null || stndata.length == 0 ? null : stndata[0].ID;
                if (stndata != null && stndata.length == 1) {
                  stnvalue = stndata[0].ID;
                  stationval = stndata[0].ID;

                }
                else if (stndata != null && stndata.length > 1) {

                  stnvalue = 0;
                  stnvalue = stndata[0].ID;
                  stationval = stndata[0].ID;
                }

                BindComponentSrNo();
                GetLineAndStationDropdown(stndata);
                funLocationsummery();

              }
              else {

              }
            },
            error: function (xhr, status, error) {
              if (xhr.status === 304) {
                //console.warn("Resource not modified. Using cached version.");
              } else {
                //console.error("Error occurred while fetching data:", error);
              }
            }
          });

        }

        $('#icon-search').dxButton({
          icon: 'search',
          hint: "Search",
          onClick() {
            var StationID = STATIONID.option('value')

            if (StationID != '' && StationID != null && StationID != undefined) {
           
              globalstn = STATIONID.option('value');
              stationval = globalstn;
             
              funLocationsummery();
            }
            else {

              $("#STATIONID").dxDropDownBox("instance").option("value", null);
              stationval = STATIONID.option('value');
              gridReworkDataSource.reload();
              funLocationsummery();
              $('#txtSrNo').text('');
              $("#txtcompsrno").dxSelectBox("instance").option("value", null);
              ToastMessage("Please select station", "Warning");

            };
          },
        });

        $('#icon-reset').dxButton({
          icon: 'undo',
          hint: 'Reset',
          onClick() {
            if (data1.length > 0) {

              STATIONID.option('value', data1[0].ID);
              var stn = STATIONID.option('value');
              stationval = STATIONID.option('value');

              funLocationsummery();

            }
            else {

              STATIONID.option('value', 0);
              stationval = STATIONID.option('value');
              var stn = STATIONID.option('value');
              funLocationsummery();
            }

            if (stn != '' && stn != null && stn != undefined) {
          
              $('#txtSrNo').text('');
              $("#txtcompsrno").dxSelectBox("instance").option("value", null);
              funLocationsummery();
              console.log(priorityEntities[0])
              
              funLocationsummery();
            }
          },
        });

        gridReworkDataSource = new DevExpress.data.DataSource({
          key: "QualityTagID",
          load: function (e) {
            customers1 = customers;
            return customers1;

          },
          insert: function (values) {

          },
          update: function (key, values) {
            var grid = $("#gridContainer").dxDataGrid('instance');
            values.QualityTagID = JSON.stringify(key);
            values.Station = grid.cellValue(grid.getRowIndexByKey(key), "Station");
            values.Tag = grid.cellValue(grid.getRowIndexByKey(key), "Tag");
            values.DataValue = grid.cellValue(grid.getRowIndexByKey(key), "DataValue");
            values.Date = grid.cellValue(grid.getRowIndexByKey(key), "Date");
            values.Time = grid.cellValue(grid.getRowIndexByKey(key), "Time");


            openCustomDialog("Confirmation", "<img src='../../../Images/Confirmation.png' style='margin-bottom: -3px;height:16px;width:16px;'></img> Are you sure you want to update ?", ["Yes", "No"]).show().done(function (dialogResult) {
              if (dialogResult.buttonText == "Yes") {
                var uri = apiRootUri + "ReworkDataReport/Update";

                $.ajax({
                  url: uri,
                  method: "POST",
                  contentType: "application/json",
                  data: JSON.stringify(values),
                  headers: {
                    "Authorization": "Bearer " + authToken
                  },
                  success: function (response, result) {
                    ToastMessage("Updated successfully.", "success");
                    gridReworkDataSource.reload();
                    funLocationsummery();

                    if (modes == 1) {
                      startTimer();
                    }

                  },
                  error: function (xhr, status, error) {
                    // Handle errors
                    if (error == 'Not Found') {
                      ToastMessage("Rework data not updated", "Warning");
                      gridReworkDataSource.reload();
                      funLocationsummery();
                    }
                    else {
                      ToastMessage("Something went wrong.Contact System admin and try again.", "Error");
                    }
                  }
                });
              }
            });



          },
          remove: function (key, values) {

          }
        });
      });

      function startTimer() {
        timerInterval = setInterval(function () {
          funLocationsummery();
        }, 10000);

      }

      function callWebmethod(url, param) {

        var url = "/Login.aspx/GetTookenDetails";
        var param = { userName: Username, password: Password, grantType: 'password', domainId: '' }; // pass user credential here
        param = JSON.stringify(param)
        var returnObj = [];
        $.ajax(
          {
            type: "POST",
            url: url,
            data: param,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            headers: {
              "Authorization": "Bearer " + authToken
            },
            success: function (result) {
              returnObj = result.d;
            },
            error: function (xhr, ajaxOptions, thrownError) {
            }
          }
        );
        return returnObj;
      }

      function Bindgrid() {

        var gridColumns = [
          {
            dataField: "QualityTagID",
            caption: "ID",
            alignment: "center",
            dataType: "number",
            visible: false,
            allowEditing: false,
            allowResizing: true,
            allowExporting: false,
            allowFiltering: false,
            showInColumnChooser: true,
            headerCellTemplate: $('<Span title="ID">ID</span>'),
          },
          {
            dataField: "Tag",
            caption: "Tag",
            alignment: "left",
            dataType: "string",
            allowEditing: false,
            allowHeaderFiltering: true,
            visible: true,
            showInColumnChooser: true,
            editorOptions: {
              showClearButton: true,
            },
            headerCellTemplate: $('<Span title="Tag">Tag</span>'),
          },
          {
            dataField: "DataValue",
            caption: "Datavalue",
            alignment: "left",
            dataType: "string",
            allowEditing: true,
            allowHeaderFiltering: true,
            visible: true,
            showInColumnChooser: true,
            editorOptions: {
              showClearButton: true,
            },
            headerCellTemplate: $('<Span title="Datavalue">Datavalue</span>'),
          },
          {
            dataField: "Station",
            caption: "Station",
            alignment: "left",
            dataType: "string",
            allowEditing: false,
            allowHeaderFiltering: true,
            visible: true,
            showInColumnChooser: true,
            editorOptions: {
              showClearButton: true,
            },
            headerCellTemplate: $('<Span title="Station">Station</span>'),
          },
          {
            dataField: "Date",
            caption: "Date",
            format: 'dd MMM yyyy',
            hint: " Allowed Date Format: MM/dd/yyyy(01/31/2022), dd MMM YYYY(01 Jan 2022)",
            alignment: "center",
            dataType: "date",
            width: "12%",
            allowHeaderFiltering: true,
            allowFiltering: true,
            allowEditing: false,
            headerCellTemplate: $('<Span title="Date">Date</span>'),
            editorOptions: {
              hint: " Allowed Date Format: MM/dd/yyyy(01/31/2022), dd MMM YYYY(01 Jan 2022)",
              applyValueMode: 'useButtons',
              showClearButton: true,

            },
          },
          {
            dataField: "Time",
            caption: "Time",
            alignment: "center",
            dataType: "time",
            format: "HH:mm:ss.SSS",
            width: "12%",
            filterOperations: ["contains", "=", "<", ">", "<=", ">=", "between"],
            allowEditing: false,
            allowFiltering: true,
            allowHeaderFiltering: false,
            headerCellTemplate: $('<Span title="Time">Time</span>'),
            showInColumnChooser: true,
            visible: true,
            editorOptions: {
              showClearButton: true,
            },
          },
        ];


        gridContainer = GetStandardDataGrid("gridContainer", pagename);
        gridContainer.option('editing.allowDeleting', false);
        gridContainer.option('editing.allowAdding', false);
        gridContainer.option('dataSource', gridReworkDataSource);
        gridContainer.option('columns', gridColumns);
        gridContainer.option('height', viewport.height - 40);


        gridContainer.option({
          onCellPrepared: function (e) {
            if (e.rowType == "header") {
              e.cellElement.css("text-align", "center");
            }
            if (e.rowType === "data" && e.column.command === "edit") {
              var isEditing = e.row.isEditing,
                $links = e.cellElement.find(".dx-link");
              if (isEditing) {

                $links.filter(".dx-link-cancel").on("click", function (args) {


                  timerInterval = setInterval(function () {
                    if (globalstn == null || globalstn == undefined) {
                      STATIONID.option("value", data1[0].ID);
                      stationval = STATIONID.option('value');
                    }
                    else {
                      STATIONID.option("value", globalstn);
                      stationval = STATIONID.option('value');
                    }
                    funLocationsummery();
                  }, 10000);

                });
              }
            }
          },
          onEditorPreparing: function (e) {

            if (e.parentType == "dataRow" && e.dataField == "DataValue") {
              e.editorOptions.maxLength = 255;
              e.editorOptions.placeholder = "Enter Datavalue.";

              e.editorOptions.onKeyDown = function (args) {
                const key = args.event.key;
                const specialCharPattern = /[!@#$%^&*()_+\-=[\]{};':"\\|,<>``~/?]/;

                if (specialCharPattern.test(key)) {
                  args.event.preventDefault();
                }
              };
            }
            if (e.parentType == "dataRow" && e.dataField == "DataValue") {
              e.editorName = "dxTextArea";
            }

          },
          onEditingStart: function (e) {
            var rowIndex = e.key; // Get the row index that is being updated

            if (rowIndex !== undefined) {

              clearInterval(timerInterval);
              // clearInterval(timerInterval1);


            }
          },
         
          onExporting: function (e) {
            if ($("#gridContainer").dxDataGrid('instance')._controllers.data._dataSource.totalCount() == 0) {
              ToastMessage("No record found !", "info", "3000", 300, { my: 'center', at: 'center', of: '#gridContainer' });
              e.cancel();
            }
            else {
              //transaction Export
              var transactionGridExport = $("#gridContainer").dxDataGrid("instance");
              transactionGridExport.beginUpdate();
              transactionGridExport.option("visible", true);
              e.component.beginUpdate();
              var workbook = new ExcelJS.Workbook();
              var worksheet = workbook.addWorksheet('Rework Data Report');

              worksheet.columns = [
                { width: 15 }, { width: 25 }, { width: 32 }, { width: 32 }, { width: 20 }
              ];

              DevExpress.excelExporter.exportDataGrid({
                component: e.component,
                worksheet: worksheet,
                keepColumnWidths: false,
                topLeftCell: { row: 1, column: 1 }

              }).then(function () {
                workbook.xlsx.writeBuffer().then(function (buffer) {
                  saveAs(new Blob([buffer], { type: "application/octet-stream" }), ("Rework Data Report.xlsx"));
                }).then(function () {
                  e.component.endUpdate();
                });
              });
              e.cancel = true;
            }
          }

        });
      }

      function getdataforrework(getdata) {
        customers = getdata;
        Bindgrid();
      }

      function bindsrno(srnodata) {

        Serialnumarr = srnodata;


        const productLabel = { 'aria-label': 'Serialnumarr' };
        var temp;

        txtcompsrno = $('#txtcompsrno').dxSelectBox({
          items: Serialnumarr,
          displayExpr: 'ComponentSrNo',
          valueExpr: 'ComponentSrNo',
          inputAttr: productLabel,
          acceptCustomValue: true,
          searchEnabled: false,
          placeholder: 'Enter or select component sr no.',
          onKeyPress(e) {
            const key = e.event.key;
            const regex = /^[a-zA-Z0-9]+$/;
            if (!regex.test(key)) {
              e.event.preventDefault();
            }
          },
          onCustomItemCreating(args) {
            
            const newValue = args.text;
            const { component } = args;
            const currentItems = component.option('items');
            const isItemInDataSource = currentItems.some((item) => item === newValue);
            let newKeyValue = { ComponentSrNo: newValue };
            if (!isItemInDataSource) {
              args.customItem = newKeyValue;
              
            }
            
          },
        });

        if (modes == 1) {
          $("#txtcompsrno").dxSelectBox("option", "disabled", true);

        }

      }

      function funLocationsummery() {
        var componentsrnum = $("#txtcompsrno").dxSelectBox("option", "value");

        var param = { stationID: stationval == null || stationval == '' || stationval == undefined ? 0 : stationval, mode: modes, componentsrno: componentsrnum }; // pass user credential here

        $.ajax({
          url: apiRootUri + "ReworkDataReport/Get",
          method: "GET",//
          data: param,
          headers: {
            "Authorization": "Bearer " + authToken
          },
          success: function (response, status, xhr) {

            if (response && xhr.status === 200) {
              getdata = response;
              if (getdata.length > 0) {

                $('#txtSrNo').text(getdata[0].ComponentSerialNo)



              }
              else {
                if (STATIONID.option('value') != null) {
                  var getnum = $("#txtcompsrno").dxSelectBox("option", "value");
                  $('#txtSrNo').text(componentsrnum)
                }
                else {
                  $('#txtSrNo').text('')
                }

              }

              getdataforrework(getdata);
            }
            else {

            }
          },
          error: function (xhr, status, error) {
            if (xhr.status === 304) {
              //console.warn("Resource not modified. Using cached version.");
            } else {
              //console.error("Error occurred while fetching data:", error);
            }
          }
        });
      }

      function callWebMethod(url, data) {
        var finalResult;
        if (data == undefined || data == "") {
          $.ajax(
            {
              type: "POST",
              url: url,
              contentType: "application/json; charset=utf-8",
              dataType: "json",
              async: false,
              headers: {
                "Authorization": "Bearer " + authToken
              },
              success: function (result) {
                finalResult = result.d;
              },
              error: function (xhr, ajaxOptions, thrownError) {
                if (xhr.statusText == 'Unauthorized') {
                  //window.location.href = "Login.aspx?Username=None";
                }
                else {

                }
              }
            }
          );
        }
        else {
          $.ajax(
            {
              type: "POST",
              url: url,
              data: "{" + data + "}",
              contentType: "application/json; charset=utf-8",
              dataType: "json",
              headers: {
                "Authorization": "Bearer " + authToken
              },
              async: false,
              success: function (result) {
                finalResult = result.d;
              },
              error: function (xhr, ajaxOptions, thrownError) {
                if (xhr.statusText == 'Unauthorized') {
                  //window.location.href = "Login.aspx?Username=None";
                }
                else {

                }
              }
            }
          );
        }
        return finalResult;
      }

      function getApplicationPath() {
        var returnValue = "";
        if (document.cookie != "") {
          cookiearray = document.cookie.split(";");
          for (i = 0; i < cookiearray.length; i++) {
            namearray = cookiearray[i].split("=");
            if (namearray[0].trim() == "ApplicationPath") {
              returnValue = namearray[1].trim();
            }
          }
        }
        return returnValue;
      }



      var Mainpath = getApplicationPath();

      if (Mainpath.slice(-1) != "/") {
        var root = Mainpath + "/";
      }
      else {
        var root = Mainpath;
      }

      function CheckSqlReference(SchemaName, TableName, id, UnwantedTable) {
        var Is_Referred = true;
        if (callWebMethod(root + "Web Services/CheckSqlReference.asmx/IsReferredInReferenceTables", "'SchemaName':'" + SchemaName + "','TableName':'" + TableName + "','id':'" + id + "','UnwantedTable':'" + UnwantedTable + "'") == false) {
          Is_Referred = false
        }
        return Is_Referred;
      }

      function getCookie(cn) {
        var name = cn + "=";
        var allCookie = decodeURIComponent(document.cookie).split(';');
        var cval = [];
        for (var i = 0; i < allCookie.length; i++) {
          if (allCookie[i].trim().indexOf(name) == 0) {
            cval = allCookie[i].trim().split("=");
          }
        }
        return (cval.length > 0) ? cval[1] : "";
      }

      function BindComponentSrNo() {

        $.ajax({
          url: apiRootUri + "ReworkDataReport/BindComponentSrNo",
          method: "GET",
          headers: {
            "Authorization": "Bearer " + authToken
          },
          success: function (response, status, xhr) {

            if (response && xhr.status === 200) {
              srnodata = response;
              bindsrno(srnodata);
            }
            else {

            }
          },
          error: function (xhr, status, error) {
            if (xhr.status === 304) {
              //console.warn("Resource not modified. Using cached version.");
            } else {
              //console.error("Error occurred while fetching data:", error);
            }
          }
        });

      }

      function BindRadioButton() {

      }

      function getViewPortHeightWidth() {

        var viewportwidth;
        var viewportheight;
        if (typeof window.innerWidth != 'undefined') {
          viewportwidth = window.innerWidth, viewportheight = window.innerHeight;
        }
        else if (typeof document.documentElement != 'undefined' && typeof document.documentElement.clientWidth != 'undefined' && document.documentElement.clientWidth != 0) {
          viewportwidth = document.documentElement.clientWidth, viewportheight = document.documentElement.clientHeight
        }
        else {
          viewportwidth = document.getElementsByTagName('body')[0].clientWidth, viewportheight = document.getElementsByTagName('body')[0].clientHeight
        }

        var viewport = { height: viewportheight, width: viewportwidth };
        viewport.height = viewportheight - 50;

        return viewport;

      }


    </script>
  </asp:PlaceHolder>
  <asp:UpdatePanel ID="UpdatePanel" runat="server">
    <ContentTemplate>
      <div class="dx-viewport">
        <div class="demo-container">

          <table align="center" width="100%" style="border: 1px solid gray">

            <tr>
              <td width="15%">
                <div style="text-align: right; font-weight: bold; margin-left: 20px" id="radio-group-change-layout"></div>
              </td>
              <td width="5%">
                <div style="text-align: right; font-weight: bold">
                  <i style="color: red">*</i>&nbsp Station &nbsp
                </div>
              </td>
              <td style="width: 8%; text-align: left">
                <div id="STATIONID" style="width: 150px;"></div>
              </td>
              <td width="10%">
                <div style="text-align: right; font-weight: bold">
                  &nbsp Component Sr No. &nbsp
                </div>
              </td>
              <td style="width: 12%; text-align: left">
                <div id="txtcompsrno" style="width: 400px;"></div>
              </td>
              <td width="2%" style="text-align: right">
                <div id="icon-search"></div>
              </td>
              <td width="2%">
                <div id="icon-reset"></div>
                <td width="7%" align="left"></td>
                <td width="62%" align="left"></td>
              </td>
            </tr>
          </table>
          <table width="100%" style="border: 1px solid gray; height: 50px">
            <tr>
              <td width="5%">
                <div style="text-align: left; font-weight: bold; font-size: larger">
                  <i style="color: red"></i>&nbsp Component Sr No.: &nbsp
                </div>
              </td>
              <td style="width: 43%; text-align: left">
                <div id="txtSrNo" style="text-align: left; font-weight: 400; font-size: x-large"></div>
              </td>
            </tr>
          </table>
          <table>
            <div id="gridContainer"></div>
          </table>
        </div>
    </ContentTemplate>
  </asp:UpdatePanel>

  <input type="hidden" id="hdn_Token" name="hdn_Token" value="" />
  <input type="hidden" id="hdn_UserName" name="hdn_UserName" value="" />
  <input type="hidden" id="hdn_DomainName" name="hdn_DomainName" value="" />
  <asp:HiddenField ID="hdn_DomainVisibility" runat="server" />
</asp:Content>
