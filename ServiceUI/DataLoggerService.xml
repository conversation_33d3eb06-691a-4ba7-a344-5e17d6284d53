<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DataLoggerService</name>
    </assembly>
    <members>
        <member name="T:DataLoggerService.Classes.DA">
            <summary>
            Project Related Logics and Customizable Class
            </summary>
        </member>
        <member name="M:DataLoggerService.DataLogger.OnStart(System.String[])">
            <summary>
            Service Initializing
            </summary>
            <param name="args"></param>
            
        </member>
        <member name="M:DataLoggerService.DataLogger.StartService">
            <summary>
            Start service and Init
            </summary>
            <returns></returns>
        </member>
        <member name="M:DataLoggerService.DataLogger.StopService">
            <summary>
            Stop service and deinit
            </summary>
            <returns></returns>
        </member>
        <member name="F:DataLoggerService.DataLogger.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:DataLoggerService.DataLogger.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:DataLoggerService.DataLogger.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:DataLoggerService.ICDTesting.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:DataLoggerService.ICDTesting.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:DataLoggerService.ICDTesting.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:DataLoggerService.Models.Enums.Enum_ICD_Tag">
            <summary>
            <Tags>Defined Enum for Tag sequence as define in tags</Tags> 
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.Enums.PLCState.Running">
            <remarks /> 
        </member>
        <member name="F:DataLoggerService.Models.Enums.PLCState.Failed">
            <remarks />
        </member>
        <member name="F:DataLoggerService.Models.Enums.PLCState.NoConfiguration">
            <remarks />
        </member>
        <member name="F:DataLoggerService.Models.Enums.PLCState.Suspended">
            <remarks />
        </member>
        <member name="F:DataLoggerService.Models.Enums.PLCState.Shutdown">
            <remarks />
        </member>
        <member name="F:DataLoggerService.Models.Enums.PLCState.Test">
            <remarks />
        </member>
        <member name="F:DataLoggerService.Models.Enums.PLCState.CommunicationFault">
            <remarks />
        </member>
        <member name="F:DataLoggerService.Models.Enums.PLCState.Unknown">
            <remarks />
        </member>
        <member name="P:DataLoggerService.Models.PLCTypes.ID">
            <summary>
            Unique id for plc type.
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.PLCTypes.Name">
            <summary>
            name.
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.PLCTypes.Value">
            <summary>
            type value 1: opcua, 2: kepware.
            </summary>
        </member>
        <member name="T:DataLoggerService.Models.StationBehaviourFunctionality">
            <summary>
            Group of functions of station.
            </summary>
            
        </member>
        <member name="P:DataLoggerService.Models.StationBehaviourFunctionality.Behaviour">
            <summary>
            Get set behaviour of station.
            </summary>
        </member>
        <member name="P:DataLoggerService.Models.StationBehaviourFunctionality.TagTypes">
            <summary>
            get set tags type based tags.
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.StationBehaviourFunctionality.TriggerTag">
            <summary>
            requested tag details.
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.StationBehaviourFunctionality.AcknowledgeTag">
            <summary>
            acknowledge tag details.
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.StationBehaviourFunctionality.ReadDataTags">
            <summary>
            all read tags of station.
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.StationBehaviourFunctionality.WriteDataTags">
            <summary>
            all write tags of station.
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.StationBehaviourFunctionality.StationQualityTag">
            <summary>
            all treaceability tags of station.
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.StationBehaviourFunctionality.ETL">
            <summary>
            Station State
            </summary>
            
        </member>
        <member name="T:DataLoggerService.Models.Line">
            <LineClass>
            Line with list of plc.
            </LineClass>
            
        </member>
        <member name="P:DataLoggerService.Models.Line.ID">
            <LineId>
            int Unique Id for Line 
            <Type>Int</Type>
            </LineId>
        </member>
        <member name="P:DataLoggerService.Models.Line.Name">
            <LineName>
             Unique Name for Line
            <Type>String</Type>
            </LineName>
        </member>
        <member name="P:DataLoggerService.Models.Line.Description">
            <Description>
            String Unique Description for Line
            <Type>String</Type>
            </Description>
        </member>
        <member name="F:DataLoggerService.Models.Line.PLCs">
            <PLCList>
            List of related plc to line
            <Type>List of PLC</Type>
            </PLCList>
        </member>
        <!-- Badly formed XML comment ignored for member "F:DataLoggerService.Models.Line.Plcs" -->
        <member name="F:DataLoggerService.Models.Line.Delays">
            <summary>
            Delay master list
            </summary>
        </member>
        <member name="P:DataLoggerService.Models.Line.ShiftTagAddress">
            <summary>
            Shift tag address.
            </summary>
        </member>
        <member name="F:DataLoggerService.Models.Line.m_shiftTag">
            <summary>
            Protected Shift tag address.
            </summary>
        </member>
        <member name="M:DataLoggerService.Models.Line.#ctor(System.Int32,System.String,System.String,System.String)">
            <summary>
            default constructor to define line info.
            </summary>
            <param name="iD">line id</param>
            <param name="name"></param>
            <param name="description"></param>
        </member>
        <member name="P:DataLoggerService.Models.Line.Shift">
            <summary>
            Shift Value.
            </summary>
        </member>
        <member name="M:DataLoggerService.Models.Line.Init">
            <summary>
            Init delays, plc, station and tags.
            </summary>
        </member>
        <member name="T:DataLoggerService.Models.PLC">
            <summary>
            PLC abstract class
            </summary>
            
        </member>
        <member name="F:DataLoggerService.Models.PLC.ID">
            <summary>
            Unique PLC ID
            </summary>
        </member>
        <member name="P:DataLoggerService.Models.PLC.modeTag">
            <summary>
            Mode Tag.
            </summary>
        </member>
        <member name="P:DataLoggerService.Models.PLC.Mode">
            <summary>
            Station Mode.
            </summary>
        </member>
        <member name="P:DataLoggerService.Models.Station.ID">
            <summary>
            Station unique id
            </summary>
        </member>
        <member name="M:DataLoggerService.Models.Station.Trigger(DataLoggerService.Models.Tag)">
            <summary>
            Event to start the process on item changes. when plc on there request.
            </summary>
            <param name="requestTag">request tag of specific functionality/subscribe tag</param>
        </member>
        <member name="M:DataLoggerService.Models.Station.StopTrigger(DataLoggerService.Models.Tag)">
            <summary>
            Event to stop/complete process. when plc reset there request.
            </summary>
            <param name="requestTag">request tag of specific functionality/subscribe tag</param>
        </member>
        <member name="M:DataLoggerService.Models.Station.TriggerThread(DataLoggerService.Models.Tag)">
            <summary>
            Event to start the process of DA. when plc on there request.
            </summary>
            <param name="requestTag">request tag of specific functionality/subscribe tag</param>
        </member>
        <member name="M:DataLoggerService.Models.Station.UpdateStationLayout(System.Collections.Generic.List{DataLoggerService.Models.Delay},System.Int32,System.String)">
            <Details> 
            Procedure should return status and log message
            <status>Status Can OK, DUPLICATE, INVALID,REJECT, REWORK</status> 
            </Details>
            <Parameter name="tags">List Station Tags</Parameter>
        </member>
        <member name="M:DataLoggerService.Models.Tag.TrackingDataInsert(System.Byte,System.String)">
            <summary>
            Insert tag values.
            </summary>
            <param name="direction">0=PLC_TO_PC(Read), 1=PC_TO_PLC(Write).</param>
            <param name="strCallerName">calling methodname.</param>
        </member>
        <member name="M:DataLoggerService.PLCController.KepwareClient.CertificateValidator_CertificateValidation(Opc.Ua.CertificateValidator,Opc.Ua.CertificateValidationEventArgs)">
            <summary>
            Handles a certificate validation error.
            </summary>
        </member>
        <member name="M:DataLoggerService.PLCController.KepwareClient.InternalDisconnect">
            <summary>
            Disconnects from the server.
            </summary>
        </member>
        <member name="E:DataLoggerService.PLCController.KepwareClient.ConnectComplete">
            <summary>
            Raised after successfully connecting to or disconnecing from a server.
            </summary>
        </member>
        <member name="P:DataLoggerService.PLCController.KepwareClient.Configuration">
            <summary>
            The client application configuration.
            </summary>
        </member>
        <member name="M:DataLoggerService.PLCController.OPCUAClient.CertificateValidator_CertificateValidation(Opc.Ua.CertificateValidator,Opc.Ua.CertificateValidationEventArgs)">
            <summary>
            Handles a certificate validation error.
            </summary>
        </member>
        <member name="M:DataLoggerService.PLCController.OPCUAClient.GetTypeDictionary(Opc.Ua.DataTypeNode,Opc.Ua.Client.Session,System.Collections.Generic.List{System.String[]})">
            <summary>Browses for the desired type dictionary to parse for containing data types</summary>
            <param name="dataTypeNode">The data type node</param>
            <param name="theSessionToBrowseIn">The current session to browse in</param>
            <param name="structureDictionary">The list of string arrays containing the structure information tag name, data type id and array dimension</param>
            <returns>The dictionary as List of string arrays</returns>
            <exception cref="T:System.Exception">Throws and forwards any exception with short error description.</exception>
        </member>
        <member name="M:DataLoggerService.PLCController.OPCUAClient.ParseDataToTagsFromDictionary(System.Collections.Generic.List{System.String[]},System.Collections.Generic.List{System.Byte[]})">
            <summary>Parses a byte array to objects containing tag names and tag data types</summary>
            <param name="varList">List of object containing tag names and tag data types</param>
            <param name="byteResult">A byte array to parse</param>
            <returns>A list of string[4]; string[0] = tag name, string[1] = tag name, string[2] = value, string[3] = opc data type</returns>
            <exception cref="T:System.Exception">Throws and forwards any exception with short error description.</exception>
        </member>
        <member name="M:DataLoggerService.PLCController.OPCUAClient.InternalDisconnect">
            <summary>
            Disconnects from the server.
            </summary>
        </member>
        <member name="E:DataLoggerService.PLCController.OPCUAClient.ConnectComplete">
            <summary>
            Raised after successfully connecting to or disconnecting from a server.
            </summary>
        </member>
        <member name="P:DataLoggerService.PLCController.OPCUAClient.Configuration">
            <summary>
            The client application configuration.
            </summary>
        </member>
        <member name="F:DataLoggerService.ProjectInstaller.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:DataLoggerService.ProjectInstaller.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:DataLoggerService.ProjectInstaller.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:DataLoggerService.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:DataLoggerService.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:DataLoggerService.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:DataLoggerService.Properties.Resources.Wipro_PARI">
            <summary>
              Looks up a localized resource of type System.Drawing.Icon similar to (Icon).
            </summary>
        </member>
        <member name="M:DataLoggerService.Connection.GenerateToken">
            <summary>
            <para>Token-based authentication is a process where the client application first sends a request to Authentication server with a valid credentials </para> 
            </summary>
            <returns>Web API Response.</returns>
        </member>
        <member name="M:DataLoggerService.GET.GetRequestData(System.String)">
            <summary>
            
            </summary>
            <param name="urlParameter"></param>
            <returns></returns>
        </member>
        <member name="F:DataLoggerService.POST.user">
            <summary>
            Post Request Data 
            <para>In this method set base address of Asp.Net Web API and sets the accept header to application/json that tells the server to send data in JSON Format. </para> 
            <para>urlParameter example "api/WebApiControllerName/MethodName?FirstParameter=" + param1 + "</para>
            </summary>
            <param name="urlParameter"></param>
            <returns>DataSet</returns>
            
        </member>
    </members>
</doc>
