﻿<?xml version="1.0" encoding="utf-8"?>
<ApplicationConfiguration
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:ua="http://opcfoundation.org/UA/2008/02/Types.xsd"
  xmlns="http://opcfoundation.org/UA/SDK/Configuration.xsd"
>
  <ApplicationName>Quickstart Reference Client</ApplicationName>
  <ApplicationUri>urn:localhost:Quickstarts:ReferenceClient</ApplicationUri>
  <ProductUri>uri:opcfoundation.org:Quickstarts:ReferenceClient</ProductUri>
  <ApplicationType>Client_1</ApplicationType>
  
  <SecurityConfiguration>

    <!-- Where the application instance certificate is stored (MachineDefault) -->
    <ApplicationCertificate>
      <StoreType>Directory</StoreType>
      <StorePath>%CommonApplicationData%\OPC Foundation\pki\own</StorePath>
      <SubjectName>CN=Quickstart Reference Client, C=US, S=Arizona, O=OPC Foundation, DC=localhost</SubjectName>
    </ApplicationCertificate>

   <!-- Where the issuer certificate are stored (certificate authorities) -->
    <TrustedIssuerCertificates>
      <StoreType>Directory</StoreType>
      <StorePath>%CommonApplicationData%\OPC Foundation\pki\issuer</StorePath>
    </TrustedIssuerCertificates>

    <!-- Where the trust list is stored -->
    <TrustedPeerCertificates>
      <StoreType>Directory</StoreType>
      <StorePath>%CommonApplicationData%\OPC Foundation\pki\trusted</StorePath>
    </TrustedPeerCertificates>

    <!-- The directory used to store invalid certficates for later review by the administrator. -->
    <RejectedCertificateStore>
      <StoreType>Directory</StoreType>
      <StorePath>%CommonApplicationData%\OPC Foundation\pki\rejected</StorePath>
    </RejectedCertificateStore>

    <!-- WARNING: The following setting (to automatically accept untrusted certificates) should be used
    for easy debugging purposes ONLY and turned off for production deployments! -->
    <AutoAcceptUntrustedCertificates>false</AutoAcceptUntrustedCertificates>
  
  </SecurityConfiguration>
  
  <TransportConfigurations></TransportConfigurations>
  
  <TransportQuotas>
    <OperationTimeout>600000</OperationTimeout>
    <MaxStringLength>1048576</MaxStringLength>
    <MaxByteStringLength>1048576</MaxByteStringLength>
    <MaxArrayLength>65535</MaxArrayLength>
    <MaxMessageSize>4194304</MaxMessageSize>
    <MaxBufferSize>65535</MaxBufferSize>
    <ChannelLifetime>300000</ChannelLifetime>
    <SecurityTokenLifetime>3600000</SecurityTokenLifetime>
  </TransportQuotas>

  <ClientConfiguration>
    <DefaultSessionTimeout>60000</DefaultSessionTimeout>
    <WellKnownDiscoveryUrls>
      <ua:String>opc.tcp://{0}:4840</ua:String>
      <ua:String>http://{0}:52601/UADiscovery</ua:String>
      <ua:String>http://{0}/UADiscovery/Default.svc</ua:String>
    </WellKnownDiscoveryUrls>
    <DiscoveryServers></DiscoveryServers>
    <EndpointCacheFilePath>Quickstarts.ReferenceClient.Endpoints.xml</EndpointCacheFilePath>
    <MinSubscriptionLifetime>10000</MinSubscriptionLifetime>
  </ClientConfiguration>

  <Extensions>
  </Extensions>

  <TraceConfiguration>
    <OutputFilePath>%CommonApplicationData%\OPC Foundation\Logs\Quickstarts.ReferenceClient.log.txt</OutputFilePath>
    <DeleteOnLoad>true</DeleteOnLoad>
    <!-- Show Only Errors -->
    <!-- <TraceMasks>1</TraceMasks> -->
    <!-- Show Only Security and Errors -->
    <!-- <TraceMasks>513</TraceMasks> -->
    <!-- Show Only Security, Errors and Trace -->
    <!-- <TraceMasks>515</TraceMasks> -->
    <!-- Show Only Security, COM Calls, Errors and Trace -->
    <!-- <TraceMasks>771</TraceMasks> -->
    <!-- Show Only Security, Service Calls, Errors and Trace -->
    <!-- <TraceMasks>523</TraceMasks> -->
    <!-- Show Only Security, ServiceResultExceptions, Errors and Trace -->
    <TraceMasks>519</TraceMasks>
  </TraceConfiguration>
  
</ApplicationConfiguration>