<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <connectionStrings>
    <!--<add name="Company1_OLTP" connectionString="Data Source=NB011622; DataBase=PRU41Simulator; persist security info=True;Integrated Security=SSPI;Max Pool Size=15000;MultipleActiveResultsets=true; Application Name=GenericReport" />
    <add name="Company1_OLAP" connectionString="Data Source=NB011622; DataBase=PRU41Simulator; persist security info=True;Integrated Security=SSPI;Max Pool Size=15000;MultipleActiveResultsets=true; Application Name=GenericReport" />-->
  </connectionStrings>
  <appSettings>
    <!--if ispublish true web api refer webapi extrenal link and false it take web api internal link-->
    <add key="Access-Control-Allow-Origin-Internal" value="http://localhost:62787/" />
    <add key="Access-Control-Allow-Origin-External" value="http://localhost:62787/" />
    <add key="IsPublish" value="False" />
    <add key="ServiceName" value="DataLoggerService" />
    <add key="Access-Control-Allow-Headers" value="content-type, accept, authorization" />
    <add key="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS" />
    <add key="UserName" value="dataloggerService" />
    <add key="password" value="1211" />
    <add key="grantType" value="password" />
    <add key="Simulation" value="False" />
    <add key="StartToolStripMenuItem" value="True" />
    <add key="ShowHide" value="true" />
    <add key="RestartToolStripMenuItem" value="true" />
    <add key="ShowSimulatorButton" value="True" />
    <add key="ShowWriteButtoninTroubleShoot" value="true" />
    <add key="ShowTroubleShootingTab" value="True" />
    <add key="PLCStartStopButtonVisible" value="True" />
    <add key="LineStartStopButtonVisible" value="True" />
    <add key="ETLFileMoveTime" value="1" />
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>